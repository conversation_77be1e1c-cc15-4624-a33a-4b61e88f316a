# Server Configuration
server.port=8080
server.servlet.context-path=/

# Application Configuration
app.name=Crete Rooms Backend
app.version=1.0.0

# Email Configuration (Gmail SMTP)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${GMAIL_USERNAME:<EMAIL>}
spring.mail.password=${GMAIL_APP_PASSWORD:your-app-password}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.trust=smtp.gmail.com

# Custom Email Settings
app.email.from=${GMAIL_USERNAME:<EMAIL>}
app.email.to=${OWNER_EMAIL:<EMAIL>}

# Logging Configuration
logging.level.com.crete.rooms=INFO
logging.level.org.springframework.mail=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# CORS Configuration
spring.web.cors.allowed-origins=http://localhost:3000
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# Development Configuration
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
