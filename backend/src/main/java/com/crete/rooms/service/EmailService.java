package com.crete.rooms.service;

import com.crete.rooms.model.Inquiry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.format.DateTimeFormatter;

@Service
public class EmailService {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);
    
    @Autowired
    private JavaMailSender mailSender;
    
    @Value("${app.email.to}")
    private String toEmail;
    
    @Value("${app.email.from}")
    private String fromEmail;
    
    public void sendInquiryEmail(Inquiry inquiry) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("New Booking Inquiry - " + inquiry.getName());
            message.setText(buildEmailContent(inquiry));
            
            mailSender.send(message);
            logger.info("Inquiry email sent successfully for: {}", inquiry.getName());
            
        } catch (Exception e) {
            logger.error("Failed to send inquiry email for: {}", inquiry.getName(), e);
            throw new RuntimeException("Failed to send email", e);
        }
    }
    
    public void sendConfirmationEmail(Inquiry inquiry) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(inquiry.getEmail());
            message.setSubject("Thank you for your inquiry - Crete Rooms");
            message.setText(buildConfirmationContent(inquiry));
            
            mailSender.send(message);
            logger.info("Confirmation email sent successfully to: {}", inquiry.getEmail());
            
        } catch (Exception e) {
            logger.error("Failed to send confirmation email to: {}", inquiry.getEmail(), e);
            // Don't throw exception here as the main inquiry was processed
            logger.warn("Continuing despite confirmation email failure");
        }
    }
    
    private String buildEmailContent(Inquiry inquiry) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        
        StringBuilder content = new StringBuilder();
        content.append("New Booking Inquiry Received\n");
        content.append("================================\n\n");
        content.append("Guest Information:\n");
        content.append("Name: ").append(inquiry.getName()).append("\n");
        content.append("Email: ").append(inquiry.getEmail()).append("\n");
        content.append("Phone: ").append(inquiry.getPhone() != null ? inquiry.getPhone() : "Not provided").append("\n\n");
        
        content.append("Booking Details:\n");
        content.append("Check-in: ").append(inquiry.getCheckIn().format(formatter)).append("\n");
        content.append("Check-out: ").append(inquiry.getCheckOut().format(formatter)).append("\n");
        content.append("Number of guests: ").append(inquiry.getGuests()).append("\n");
        content.append("Preferred room: ").append(inquiry.getRoom() != null && !inquiry.getRoom().isEmpty() ? 
                getRoomDisplayName(inquiry.getRoom()) : "Any available room").append("\n\n");
        
        if (inquiry.getMessage() != null && !inquiry.getMessage().trim().isEmpty()) {
            content.append("Special Requests/Message:\n");
            content.append(inquiry.getMessage()).append("\n\n");
        }
        
        content.append("Please respond to this inquiry within a few minutes as promised.\n");
        content.append("\nBest regards,\n");
        content.append("Relax Project Crete Booking System");
        
        return content.toString();
    }
    
    private String buildConfirmationContent(Inquiry inquiry) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        
        StringBuilder content = new StringBuilder();
        content.append("Dear ").append(inquiry.getName()).append(",\n\n");
        content.append("Thank you for your inquiry about staying at Relax Project Crete!\n\n");
        content.append("We have received your booking request with the following details:\n\n");
        content.append("Check-in: ").append(inquiry.getCheckIn().format(formatter)).append("\n");
        content.append("Check-out: ").append(inquiry.getCheckOut().format(formatter)).append("\n");
        content.append("Number of guests: ").append(inquiry.getGuests()).append("\n");
        content.append("Preferred room: ").append(inquiry.getRoom() != null && !inquiry.getRoom().isEmpty() ? 
                getRoomDisplayName(inquiry.getRoom()) : "Any available room").append("\n\n");
        
        content.append("We will respond to your inquiry within a few minutes with availability ");
        content.append("and pricing information during our business hours (9 AM - 9 PM Greek time).\n\n");
        content.append("If you have any urgent questions, please don't hesitate to contact us directly:\n");
        content.append("Phone/WhatsApp: +30 ************\n");
        content.append("Email: <EMAIL>\n\n");
        content.append("We look forward to hosting you in beautiful Crete!\n\n");
        content.append("Best regards,\n");
        content.append("The Relax Project Crete Team");
        
        return content.toString();
    }
    
    private String getRoomDisplayName(String roomId) {
        switch (roomId) {
            case "relax-by-lake":
                return "Relax by the lake";
            case "relax-mulberry-studio":
                return "Relax under the mulberry tree - Studio";
            case "relax-mulberry-home":
                return "Relax under the mulberry tree - Home";
            default:
                return roomId;
        }
    }
}
