package com.crete.rooms.controller;

import com.crete.rooms.model.Inquiry;
import com.crete.rooms.service.EmailService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "http://localhost:3000")
public class InquiryController {
    
    private static final Logger logger = LoggerFactory.getLogger(InquiryController.class);
    
    @Autowired
    private EmailService emailService;
    
    @PostMapping("/inquiry")
    public ResponseEntity<Map<String, String>> submitInquiry(@Valid @RequestBody Inquiry inquiry) {
        logger.info("Received inquiry from: {}", inquiry.getName());
        
        try {
            // Validate dates
            if (inquiry.getCheckIn().isBefore(LocalDate.now())) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "Check-in date cannot be in the past");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            if (inquiry.getCheckOut().isBefore(inquiry.getCheckIn()) || 
                inquiry.getCheckOut().isEqual(inquiry.getCheckIn())) {
                Map<String, String> errorResponse = new HashMap<>();
                errorResponse.put("error", "Check-out date must be after check-in date");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            // Send emails
            emailService.sendInquiryEmail(inquiry);
            emailService.sendConfirmationEmail(inquiry);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Inquiry submitted successfully");
            response.put("status", "success");
            
            logger.info("Inquiry processed successfully for: {}", inquiry.getName());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error processing inquiry for: {}", inquiry.getName(), e);
            
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to process inquiry. Please try again or contact us directly.");
            errorResponse.put("status", "error");
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> healthCheck() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "healthy");
        response.put("service", "Crete Rooms Backend");
        return ResponseEntity.ok(response);
    }
}
