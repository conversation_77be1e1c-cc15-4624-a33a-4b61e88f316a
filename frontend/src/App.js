import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import './App.css';
import Navigation from './components/Navigation';
import AboutUs from './components/AboutUs';
import Rooms from './components/Rooms';
import ContactUs from './components/ContactUs';
import Recommendations from './components/Recommendations';
import BookNow from './components/BookNow';

function App() {
  return (
    <Router>
      <div className="app">
        <header className="header">
          <div className="header-content">
            <Link to="/" className="logo-link">
              <h1 className="logo">Relax Project Crete</h1>
            </Link>
            <p className="tagline">Your Perfect Vacation Stay in Beautiful Crete</p>
          </div>
        </header>

        <Navigation />

        <main className="main-content">
          <Routes>
            <Route path="/" element={<AboutUs />} />
            <Route path="/about" element={<AboutUs />} />
            <Route path="/rooms" element={<Rooms />} />
            <Route path="/contact" element={<ContactUs />} />
            <Route path="/recommendations" element={<Recommendations />} />
            <Route path="/book" element={<BookNow />} />
          </Routes>
        </main>

        <footer className="footer">
          <p>&copy; 2024 Relax Project Crete. All rights reserved. | Experience the beauty of Crete</p>
        </footer>
      </div>
    </Router>
  );
}

export default App;
