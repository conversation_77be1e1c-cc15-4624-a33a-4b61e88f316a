import React, { useState } from 'react';
import './App.css';
import Navigation from './components/Navigation';
import AboutUs from './components/AboutUs';
import Rooms from './components/Rooms';
import ContactUs from './components/ContactUs';
import Recommendations from './components/Recommendations';
import BookNow from './components/BookNow';

function App() {
  const [activeTab, setActiveTab] = useState('about');

  const renderActiveSection = () => {
    switch (activeTab) {
      case 'about':
        return <AboutUs />;
      case 'rooms':
        return <Rooms />;
      case 'contact':
        return <ContactUs />;
      case 'recommendations':
        return <Recommendations />;
      case 'book':
        return <BookNow />;
      default:
        return <AboutUs />;
    }
  };

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <h1 className="logo">Crete Rooms</h1>
          <p className="tagline">Your Perfect Vacation Stay in Beautiful Crete</p>
        </div>
      </header>
      
      <Navigation activeTab={activeTab} setActiveTab={setActiveTab} />
      
      <main className="main-content">
        {renderActiveSection()}
      </main>
      
      <footer className="footer">
        <p>&copy; 2024 Crete Rooms. All rights reserved. | Experience the beauty of Crete</p>
      </footer>
    </div>
  );
}

export default App;
