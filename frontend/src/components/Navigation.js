import React from 'react';

const Navigation = ({ activeTab, setActiveTab }) => {
  const tabs = [
    { id: 'about', label: 'About Us' },
    { id: 'rooms', label: 'Rooms' },
    { id: 'contact', label: 'Contact Us' },
    { id: 'recommendations', label: 'Recommendations Around' },
    { id: 'book', label: 'Book Now' }
  ];

  return (
    <nav className="navigation">
      <div className="nav-container">
        <ul className="nav-tabs">
          {tabs.map(tab => (
            <li key={tab.id} className="nav-tab">
              <button
                className={`nav-button ${activeTab === tab.id ? 'active' : ''}`}
                onClick={() => setActiveTab(tab.id)}
              >
                {tab.label}
              </button>
            </li>
          ))}
        </ul>
      </div>
    </nav>
  );
};

export default Navigation;
