import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navigation = () => {
  const location = useLocation();

  const tabs = [
    { id: 'about', label: 'About Us', path: '/about' },
    { id: 'rooms', label: 'Rooms', path: '/rooms' },
    { id: 'contact', label: 'Contact Us', path: '/contact' },
    { id: 'recommendations', label: 'Recommendations Around', path: '/recommendations' },
    { id: 'book', label: 'Book Now', path: '/book' }
  ];

  const isActive = (path) => {
    if (path === '/about') {
      return location.pathname === '/' || location.pathname === '/about';
    }
    return location.pathname === path;
  };

  return (
    <nav className="navigation">
      <div className="nav-container">
        <ul className="nav-tabs">
          {tabs.map(tab => (
            <li key={tab.id} className="nav-tab">
              <Link
                to={tab.path}
                className={`nav-button ${isActive(tab.path) ? 'active' : ''}`}
              >
                {tab.label}
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </nav>
  );
};

export default Navigation;
