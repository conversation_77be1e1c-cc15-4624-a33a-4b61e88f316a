import React, { useState } from 'react';
import axios from 'axios';

const BookNow = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    checkIn: '',
    checkOut: '',
    guests: '2',
    room: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const rooms = [
    { id: 'olive-grove', name: 'Olive Grove Apartment' },
    { id: 'sea-breeze', name: 'Sea Breeze Studio' },
    { id: 'mountain-view', name: 'Mountain View Villa' }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      const response = await axios.post('/api/inquiry', formData);
      
      if (response.status === 200) {
        setSubmitStatus('success');
        setFormData({
          name: '',
          email: '',
          phone: '',
          checkIn: '',
          checkOut: '',
          guests: '2',
          room: '',
          message: ''
        });
      }
    } catch (error) {
      console.error('Error submitting inquiry:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="section">
      <h2 className="section-title">Book Now</h2>
      <div className="section-content">
        <p>
          Ready to experience the beauty of Crete? Send us your inquiry and we'll respond within 
          a few minutes with availability and pricing information. We look forward to hosting you!
        </p>
        
        <div style={{ 
          backgroundColor: '#F5F5DC', 
          padding: '1.5rem', 
          borderRadius: '10px', 
          margin: '2rem 0',
          textAlign: 'center'
        }}>
          <h3 style={{ color: '#9CAF88', marginBottom: '1rem' }}>Quick Response Guarantee</h3>
          <p style={{ margin: 0 }}>
            We respond to all inquiries within a few minutes during business hours (9 AM - 9 PM Greek time)
          </p>
        </div>

        {submitStatus === 'success' && (
          <div className="success-message">
            <strong>Thank you for your inquiry!</strong><br />
            We've received your request and will respond within a few minutes with availability and pricing information.
          </div>
        )}

        {submitStatus === 'error' && (
          <div style={{ 
            backgroundColor: '#f8d7da', 
            color: '#721c24', 
            padding: '1rem', 
            borderRadius: '5px', 
            marginBottom: '1rem',
            textAlign: 'center'
          }}>
            There was an error sending your inquiry. Please try again or contact us directly.
          </div>
        )}

        <form className="contact-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="form-label" htmlFor="name">Full Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              className="form-input"
              value={formData.name}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="form-group">
            <label className="form-label" htmlFor="email">Email Address *</label>
            <input
              type="email"
              id="email"
              name="email"
              className="form-input"
              value={formData.email}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="form-group">
            <label className="form-label" htmlFor="phone">Phone Number</label>
            <input
              type="tel"
              id="phone"
              name="phone"
              className="form-input"
              value={formData.phone}
              onChange={handleInputChange}
              placeholder="+30 ************"
            />
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
            <div className="form-group">
              <label className="form-label" htmlFor="checkIn">Check-in Date *</label>
              <input
                type="date"
                id="checkIn"
                name="checkIn"
                className="form-input"
                value={formData.checkIn}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label className="form-label" htmlFor="checkOut">Check-out Date *</label>
              <input
                type="date"
                id="checkOut"
                name="checkOut"
                className="form-input"
                value={formData.checkOut}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
            <div className="form-group">
              <label className="form-label" htmlFor="guests">Number of Guests *</label>
              <select
                id="guests"
                name="guests"
                className="form-input"
                value={formData.guests}
                onChange={handleInputChange}
                required
              >
                <option value="1">1 Guest</option>
                <option value="2">2 Guests</option>
                <option value="3">3 Guests</option>
                <option value="4">4 Guests</option>
                <option value="5">5 Guests</option>
                <option value="6">6+ Guests</option>
              </select>
            </div>

            <div className="form-group">
              <label className="form-label" htmlFor="room">Preferred Room</label>
              <select
                id="room"
                name="room"
                className="form-input"
                value={formData.room}
                onChange={handleInputChange}
              >
                <option value="">Any Available Room</option>
                {rooms.map(room => (
                  <option key={room.id} value={room.id}>{room.name}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="form-group">
            <label className="form-label" htmlFor="message">Special Requests or Questions</label>
            <textarea
              id="message"
              name="message"
              className="form-textarea"
              value={formData.message}
              onChange={handleInputChange}
              placeholder="Let us know about any special requirements, questions about the area, or how we can make your stay perfect..."
            />
          </div>

          <button 
            type="submit" 
            className="submit-button"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Sending Inquiry...' : 'Send Inquiry'}
          </button>
        </form>
      </div>
    </section>
  );
};

export default BookNow;
