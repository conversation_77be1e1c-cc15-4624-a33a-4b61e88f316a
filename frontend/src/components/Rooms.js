import React from 'react';

const Rooms = () => {
  const rooms = [
    {
      id: 1,
      title: "Olive Grove Apartment",
      description: "A charming 2-bedroom apartment with stunning views of ancient olive groves. Perfect for couples or small families seeking tranquility.",
      amenities: [
        "2 Bedrooms",
        "1 Bathroom",
        "Fully equipped kitchen",
        "Private balcony with olive grove views",
        "Air conditioning",
        "Free Wi-Fi",
        "Parking space",
        "Traditional Cretan decor"
      ]
    },
    {
      id: 2,
      title: "Sea Breeze Studio",
      description: "A modern studio apartment just minutes from the beach. Ideal for couples looking for a romantic seaside retreat.",
      amenities: [
        "Open-plan living space",
        "Queen-size bed",
        "Kitchenette",
        "Private terrace",
        "Sea views",
        "Air conditioning",
        "Free Wi-Fi",
        "Beach equipment provided"
      ]
    },
    {
      id: 3,
      title: "Mountain View Villa",
      description: "Spacious 3-bedroom villa with panoramic mountain views. Perfect for larger groups and families who want space and privacy.",
      amenities: [
        "3 Bedrooms",
        "2 Bathrooms",
        "Large living room",
        "Full kitchen with dining area",
        "Private garden",
        "Mountain views",
        "Air conditioning throughout",
        "Free Wi-Fi",
        "BBQ facilities",
        "Private parking"
      ]
    }
  ];

  return (
    <section className="section">
      <h2 className="section-title">Our Rooms</h2>
      <div className="section-content">
        <p>
          Discover our three unique accommodations, each designed to provide you with comfort, 
          privacy, and authentic Cretan charm. All apartments are fully furnished and equipped 
          with modern amenities while maintaining traditional character.
        </p>
      </div>
      
      <div className="rooms-grid">
        {rooms.map(room => (
          <div key={room.id} className="room-card">
            <div className="room-image">
              [Placeholder Image - {room.title}]
            </div>
            <div className="room-info">
              <h3 className="room-title">{room.title}</h3>
              <p>{room.description}</p>
              <ul className="amenities">
                {room.amenities.map((amenity, index) => (
                  <li key={index}>{amenity}</li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default Rooms;
