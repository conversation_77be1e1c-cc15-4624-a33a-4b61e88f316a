import React from 'react';

const Rooms = () => {
  const rooms = [
    {
      id: 1,
      title: "Relax by the lake",
      description: "A serene lakeside retreat offering tranquility and stunning water views. Perfect for those seeking peace and natural beauty.",
      amenities: [
        "2 Bedrooms",
        "1 Bathroom",
        "Fully equipped kitchen",
        "Private balcony with lake views",
        "Air conditioning",
        "Free Wi-Fi",
        "Parking space",
        "Traditional Cretan decor"
      ]
    },
    {
      id: 2,
      title: "Relax under the mulberry tree - Studio",
      description: "A cozy studio nestled under the shade of ancient mulberry trees. Ideal for couples seeking an intimate and peaceful getaway.",
      amenities: [
        "Open-plan living space",
        "Queen-size bed",
        "Kitchenette",
        "Private terrace under mulberry trees",
        "Garden views",
        "Air conditioning",
        "Free Wi-Fi",
        "Natural shade and privacy"
      ]
    },
    {
      id: 3,
      title: "Relax under the mulberry tree - Home",
      description: "A spacious family home surrounded by beautiful mulberry trees. Perfect for larger groups and families who want comfort and tranquility.",
      amenities: [
        "3 Bedrooms",
        "2 Bathrooms",
        "Large living room",
        "Full kitchen with dining area",
        "Private garden with mulberry trees",
        "Peaceful garden views",
        "Air conditioning throughout",
        "Free Wi-Fi",
        "BBQ facilities",
        "Private parking"
      ]
    }
  ];

  return (
    <section className="section">
      <h2 className="section-title">Our Rooms</h2>
      <div className="section-content">
        <p>
          Discover our three unique properties, each designed to provide you with comfort,
          privacy, and authentic Cretan charm. All accommodations are fully furnished and equipped
          with modern amenities while maintaining traditional character and natural beauty.
        </p>
      </div>
      
      <div className="rooms-grid">
        {rooms.map(room => (
          <div key={room.id} className="room-card">
            <div className="room-image">
              [Placeholder Image - {room.title}]
            </div>
            <div className="room-info">
              <h3 className="room-title">{room.title}</h3>
              <p>{room.description}</p>
              <ul className="amenities">
                {room.amenities.map((amenity, index) => (
                  <li key={index}>{amenity}</li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default Rooms;
