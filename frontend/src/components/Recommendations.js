import React, { useState } from 'react';

const Recommendations = () => {
  const [selectedRecommendation, setSelectedRecommendation] = useState(null);
  const [activeFilter, setActiveFilter] = useState('all');

  const recommendations = [
    // Beaches
    {
      id: 1,
      title: "Balos Lagoon",
      type: "Beach",
      category: "beaches",
      description: "One of the most beautiful beaches in the world with crystal clear waters.",
      details: "This stunning lagoon is accessible by boat or a challenging hike. The turquoise waters and pink sand create a paradise-like setting. Best visited early morning to avoid crowds. Bring water and sun protection as there are no facilities.",
      location: "Gramvousa Peninsula",
      distance: "1.5 hours drive + boat/hike",
      priceRange: "Free (boat trip €25)"
    },
    {
      id: 2,
      title: "Falassarna Beach",
      type: "Beach",
      category: "beaches",
      description: "Stunning sunset beach with golden sand and crystal clear waters.",
      details: "One of Crete's most famous beaches, known for its incredible sunsets and wide sandy shore. Perfect for swimming, sunbathing, and beach sports. The beach has facilities including umbrellas, sunbeds, and beach bars.",
      location: "West Coast",
      distance: "45 minutes drive",
      priceRange: "Free (sunbeds €8)"
    },
    {
      id: 3,
      title: "Seitan Limania Beach",
      type: "Beach",
      category: "beaches",
      description: "Hidden paradise beach with turquoise waters and dramatic cliffs.",
      details: "A secluded beach accessible by a short hike down steep cliffs. The reward is pristine turquoise waters and a small sandy cove surrounded by dramatic rock formations. Bring water and snacks as there are no facilities.",
      location: "Akrotiri Peninsula",
      distance: "30 minutes drive + 15 min hike",
      priceRange: "Free"
    },

    // Food and Drinks
    {
      id: 4,
      title: "Taverna Akrogiali",
      type: "Restaurant",
      category: "food-drinks",
      description: "Authentic Cretan cuisine with fresh seafood and traditional dishes.",
      details: "Located right on the beach, this family-run taverna has been serving authentic Cretan cuisine for over 30 years. Try their famous lamb with stamnagathi (wild greens) and fresh catch of the day. The sunset views are spectacular!",
      location: "Chania Old Harbor",
      distance: "5 minutes walk",
      priceRange: "€€"
    },
    {
      id: 5,
      title: "Kouzina e.p.e",
      type: "Restaurant",
      category: "food-drinks",
      description: "Modern Greek cuisine with a creative twist and excellent wine selection.",
      details: "Chef Maria creates innovative dishes using local ingredients. The tasting menu is highly recommended. Excellent wine list featuring Cretan varieties. Reservations essential, especially during summer months.",
      location: "Chania New Town",
      distance: "15 minutes drive",
      priceRange: "€€€"
    },
    {
      id: 6,
      title: "To Maridaki",
      type: "Cafe",
      category: "food-drinks",
      description: "Traditional coffee house with the best Greek coffee and pastries.",
      details: "This local favorite serves traditional Greek coffee, fresh pastries, and light meals. The owner, Yannis, has been roasting coffee for 40 years. Try the bougatsa (custard pastry) - it's legendary among locals!",
      location: "Chania Old Town",
      distance: "8 minutes walk",
      priceRange: "€"
    },

    // Attractions
    {
      id: 7,
      title: "Chania Old Town",
      type: "Historic Site",
      category: "attractions",
      description: "Venetian harbor and old town with charming narrow streets and shops.",
      details: "Wander through the maze of narrow streets filled with boutiques, cafes, and galleries. The Venetian lighthouse and harbor are perfect for evening strolls. Don't miss the Municipal Market for local products and souvenirs.",
      location: "Chania Center",
      distance: "10 minutes drive",
      priceRange: "Free"
    },
    {
      id: 8,
      title: "Arkadi Monastery",
      type: "Monastery",
      category: "attractions",
      description: "Historic monastery with beautiful architecture and important cultural significance.",
      details: "This 16th-century monastery is one of Crete's most significant historical sites. Known for its beautiful Venetian and Byzantine architecture, it played a crucial role in Crete's struggle for independence. The peaceful gardens and museum are worth exploring.",
      location: "Rethymno Province",
      distance: "1 hour drive",
      priceRange: "€3 entrance fee"
    },

    // Activities
    {
      id: 9,
      title: "Samaria Gorge",
      type: "Hiking Trail",
      category: "activities",
      description: "Famous hiking trail through Europe's longest gorge.",
      details: "A challenging 16km hike through stunning natural scenery. The trail takes 5-7 hours and ends at Agia Roumeli beach. Wear good hiking shoes and bring plenty of water. The gorge is closed in winter due to weather conditions.",
      location: "White Mountains",
      distance: "1 hour drive to starting point",
      priceRange: "€5 entrance fee"
    },
    {
      id: 10,
      title: "Scuba Diving at Stavros",
      type: "Water Sports",
      category: "activities",
      description: "Explore underwater caves and marine life in crystal clear waters.",
      details: "Professional diving center offering courses for beginners and guided dives for experienced divers. Explore underwater caves, ancient amphoras, and rich marine life. Equipment and instruction included.",
      location: "Stavros Beach",
      distance: "25 minutes drive",
      priceRange: "€45-65"
    },

    // Have Fun
    {
      id: 11,
      title: "Boat Trip to Gramvousa",
      type: "Boat Excursion",
      category: "have-fun",
      description: "Exciting boat trip to pirate island with swimming and exploration.",
      details: "Full-day boat excursion to the historic Gramvousa island with its Venetian fortress and pirate history. Includes stops at Balos Lagoon for swimming and snorkeling. Lunch and drinks included on most trips.",
      location: "Kissamos Port",
      distance: "1 hour drive",
      priceRange: "€35-45"
    },
    {
      id: 12,
      title: "Wine Tasting at Manousakis Winery",
      type: "Winery",
      category: "have-fun",
      description: "Premium wine tasting experience with vineyard tours.",
      details: "Discover award-winning Cretan wines at this boutique winery. The experience includes vineyard tours, wine education, and tasting of 5-6 premium wines paired with local cheeses and olives. Beautiful mountain views.",
      location: "Vatolakkos",
      distance: "20 minutes drive",
      priceRange: "€25-35"
    },

    // Buy Local Products
    {
      id: 13,
      title: "Chania Municipal Market",
      type: "Market",
      category: "buy-local",
      description: "Traditional covered market with local products and souvenirs.",
      details: "Housed in a beautiful 19th-century building, this market offers the best local products: olive oil, honey, herbs, traditional sweets, and handmade crafts. Perfect for authentic Cretan souvenirs and gifts.",
      location: "Chania Center",
      distance: "10 minutes drive",
      priceRange: "€-€€"
    },
    {
      id: 14,
      title: "Cretan Olive Oil Farm",
      type: "Farm Visit",
      category: "buy-local",
      description: "Traditional olive oil farm with tastings and local products.",
      details: "Visit a family-run olive oil farm to learn about traditional production methods. Includes olive oil tasting, tour of the groves, and opportunity to buy premium extra virgin olive oil directly from the producers.",
      location: "Kolymvari",
      distance: "25 minutes drive",
      priceRange: "€15 (tour + tasting)"
    }
  ];

  const categories = [
    { id: 'all', label: 'All Recommendations', icon: '🌟' },
    { id: 'beaches', label: 'Beaches', icon: '🏖️' },
    { id: 'food-drinks', label: 'Food & Drinks', icon: '🍽️' },
    { id: 'attractions', label: 'Attractions', icon: '🏛️' },
    { id: 'activities', label: 'Activities', icon: '🥾' },
    { id: 'have-fun', label: 'Have Fun', icon: '🎉' },
    { id: 'buy-local', label: 'Buy Local Products', icon: '🛍️' }
  ];

  const filteredRecommendations = activeFilter === 'all'
    ? recommendations
    : recommendations.filter(rec => rec.category === activeFilter);

  const handleRecommendationClick = (recommendation) => {
    setSelectedRecommendation(recommendation);
  };

  const closeModal = () => {
    setSelectedRecommendation(null);
  };

  return (
    <section className="section">
      <h2 className="section-title">Recommendations Around</h2>
      <div className="section-content">
        <p>
          Discover the best of Crete with our carefully curated recommendations. From authentic
          tavernas serving traditional cuisine to breathtaking natural wonders, we've selected
          the must-visit places that will make your stay truly memorable.
        </p>
      </div>

      {/* Category Filters */}
      <div className="filter-container">
        <div className="filter-buttons">
          {categories.map(category => (
            <button
              key={category.id}
              className={`filter-button ${activeFilter === category.id ? 'active' : ''}`}
              onClick={() => setActiveFilter(category.id)}
            >
              <span className="filter-icon">{category.icon}</span>
              <span className="filter-label">{category.label}</span>
            </button>
          ))}
        </div>
      </div>
      
      <div className="recommendations-grid">
        {filteredRecommendations.map(recommendation => (
          <div 
            key={recommendation.id} 
            className="recommendation-card"
            onClick={() => handleRecommendationClick(recommendation)}
          >
            <div className="recommendation-type">{recommendation.type}</div>
            <h3 className="recommendation-title">{recommendation.title}</h3>
            <p>{recommendation.description}</p>
            <div style={{ marginTop: '1rem', fontSize: '0.9rem', color: '#666' }}>
              <strong>Distance:</strong> {recommendation.distance}<br />
              <strong>Price:</strong> {recommendation.priceRange}
            </div>
          </div>
        ))}
      </div>

      {selectedRecommendation && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '2rem'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '10px',
            padding: '2rem',
            maxWidth: '600px',
            maxHeight: '80vh',
            overflow: 'auto',
            position: 'relative'
          }}>
            <button 
              onClick={closeModal}
              style={{
                position: 'absolute',
                top: '1rem',
                right: '1rem',
                background: 'none',
                border: 'none',
                fontSize: '1.5rem',
                cursor: 'pointer',
                color: '#9CAF88'
              }}
            >
              ×
            </button>
            
            <div className="recommendation-type" style={{ marginBottom: '1rem' }}>
              {selectedRecommendation.type}
            </div>
            <h3 style={{ color: '#9CAF88', marginBottom: '1rem', fontSize: '1.8rem' }}>
              {selectedRecommendation.title}
            </h3>
            <p style={{ marginBottom: '1.5rem', lineHeight: '1.6' }}>
              {selectedRecommendation.details}
            </p>
            
            <div style={{ 
              backgroundColor: '#F5F5DC', 
              padding: '1rem', 
              borderRadius: '5px',
              fontSize: '0.95rem'
            }}>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Location:</strong> {selectedRecommendation.location}
              </div>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Distance:</strong> {selectedRecommendation.distance}
              </div>
              <div>
                <strong>Price Range:</strong> {selectedRecommendation.priceRange}
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default Recommendations;
