import React, { useState } from 'react';

const Recommendations = () => {
  const [selectedRecommendation, setSelectedRecommendation] = useState(null);

  const recommendations = [
    {
      id: 1,
      title: "Taverna Akrogiali",
      type: "Restaurant",
      category: "food",
      description: "Authentic Cretan cuisine with fresh seafood and traditional dishes.",
      details: "Located right on the beach, this family-run taverna has been serving authentic Cretan cuisine for over 30 years. Try their famous lamb with stamnagathi (wild greens) and fresh catch of the day. The sunset views are spectacular!",
      location: "Chania Old Harbor",
      distance: "5 minutes walk",
      priceRange: "€€"
    },
    {
      id: 2,
      title: "Balos Lagoon",
      type: "Beach",
      category: "attraction",
      description: "One of the most beautiful beaches in the world with crystal clear waters.",
      details: "This stunning lagoon is accessible by boat or a challenging hike. The turquoise waters and pink sand create a paradise-like setting. Best visited early morning to avoid crowds. Bring water and sun protection as there are no facilities.",
      location: "Gramvousa Peninsula",
      distance: "1.5 hours drive + boat/hike",
      priceRange: "Free (boat trip €25)"
    },
    {
      id: 3,
      title: "Chania Old Town",
      type: "Historic Site",
      category: "attraction",
      description: "Venetian harbor and old town with charming narrow streets and shops.",
      details: "Wander through the maze of narrow streets filled with boutiques, cafes, and galleries. The Venetian lighthouse and harbor are perfect for evening strolls. Don't miss the Municipal Market for local products and souvenirs.",
      location: "Chania Center",
      distance: "10 minutes drive",
      priceRange: "Free"
    },
    {
      id: 4,
      title: "Kouzina e.p.e",
      type: "Restaurant",
      category: "food",
      description: "Modern Greek cuisine with a creative twist and excellent wine selection.",
      details: "Chef Maria creates innovative dishes using local ingredients. The tasting menu is highly recommended. Excellent wine list featuring Cretan varieties. Reservations essential, especially during summer months.",
      location: "Chania New Town",
      distance: "15 minutes drive",
      priceRange: "€€€"
    },
    {
      id: 5,
      title: "Samaria Gorge",
      type: "Nature",
      category: "attraction",
      description: "Famous hiking trail through Europe's longest gorge.",
      details: "A challenging 16km hike through stunning natural scenery. The trail takes 5-7 hours and ends at Agia Roumeli beach. Wear good hiking shoes and bring plenty of water. The gorge is closed in winter due to weather conditions.",
      location: "White Mountains",
      distance: "1 hour drive to starting point",
      priceRange: "€5 entrance fee"
    },
    {
      id: 6,
      title: "To Maridaki",
      type: "Cafe",
      category: "food",
      description: "Traditional coffee house with the best Greek coffee and pastries.",
      details: "This local favorite serves traditional Greek coffee, fresh pastries, and light meals. The owner, Yannis, has been roasting coffee for 40 years. Try the bougatsa (custard pastry) - it's legendary among locals!",
      location: "Chania Old Town",
      distance: "8 minutes walk",
      priceRange: "€"
    }
  ];

  const handleRecommendationClick = (recommendation) => {
    setSelectedRecommendation(recommendation);
  };

  const closeModal = () => {
    setSelectedRecommendation(null);
  };

  return (
    <section className="section">
      <h2 className="section-title">Recommendations Around</h2>
      <div className="section-content">
        <p>
          Discover the best of Crete with our carefully curated recommendations. From authentic 
          tavernas serving traditional cuisine to breathtaking natural wonders, we've selected 
          the must-visit places that will make your stay truly memorable.
        </p>
      </div>
      
      <div className="recommendations-grid">
        {recommendations.map(recommendation => (
          <div 
            key={recommendation.id} 
            className="recommendation-card"
            onClick={() => handleRecommendationClick(recommendation)}
          >
            <div className="recommendation-type">{recommendation.type}</div>
            <h3 className="recommendation-title">{recommendation.title}</h3>
            <p>{recommendation.description}</p>
            <div style={{ marginTop: '1rem', fontSize: '0.9rem', color: '#666' }}>
              <strong>Distance:</strong> {recommendation.distance}<br />
              <strong>Price:</strong> {recommendation.priceRange}
            </div>
          </div>
        ))}
      </div>

      {selectedRecommendation && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '2rem'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '10px',
            padding: '2rem',
            maxWidth: '600px',
            maxHeight: '80vh',
            overflow: 'auto',
            position: 'relative'
          }}>
            <button 
              onClick={closeModal}
              style={{
                position: 'absolute',
                top: '1rem',
                right: '1rem',
                background: 'none',
                border: 'none',
                fontSize: '1.5rem',
                cursor: 'pointer',
                color: '#9CAF88'
              }}
            >
              ×
            </button>
            
            <div className="recommendation-type" style={{ marginBottom: '1rem' }}>
              {selectedRecommendation.type}
            </div>
            <h3 style={{ color: '#9CAF88', marginBottom: '1rem', fontSize: '1.8rem' }}>
              {selectedRecommendation.title}
            </h3>
            <p style={{ marginBottom: '1.5rem', lineHeight: '1.6' }}>
              {selectedRecommendation.details}
            </p>
            
            <div style={{ 
              backgroundColor: '#F5F5DC', 
              padding: '1rem', 
              borderRadius: '5px',
              fontSize: '0.95rem'
            }}>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Location:</strong> {selectedRecommendation.location}
              </div>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Distance:</strong> {selectedRecommendation.distance}
              </div>
              <div>
                <strong>Price Range:</strong> {selectedRecommendation.priceRange}
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default Recommendations;
