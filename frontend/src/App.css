/* Mediterranean Island Theme: Olive Green, Sea Blue, White, Beige */

:root {
  --primary-olive: #9CAF88;
  --light-olive: #B8C5A6;
  --dark-olive: #7A8B6B;
  --sea-blue: #4A90B8;
  --light-sea-blue: #6BA3C7;
  --deep-sea-blue: #2E6B8F;
  --ocean-teal: #5B9AA0;
  --sky-blue: #87CEEB;
  --cream-white: #FEFEFE;
  --warm-beige: #F5F5DC;
  --soft-beige: #F0F0E8;
  --text-dark: #2C3E2D;
  --text-medium: #4A5D4B;
  --text-light: #6B7C6C;
  --accent-gold: #D4AF37;
  --shadow-light: rgba(74, 144, 184, 0.1);
  --shadow-medium: rgba(74, 144, 184, 0.2);
  --shadow-blue: rgba(46, 107, 143, 0.15);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: #FFFFFF;
  color: #333;
  line-height: 1.6;
}

.app {
  min-height: 100vh;
}

/* Header Styles */
.header {
  background: linear-gradient(135deg, var(--sea-blue) 0%, var(--ocean-teal) 50%, var(--primary-olive) 100%);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 4px 20px var(--shadow-blue);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.logo {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.tagline {
  font-size: 1.1rem;
  font-weight: 300;
  opacity: 0.9;
}

/* Navigation Styles */
.navigation {
  background: linear-gradient(90deg, var(--warm-beige) 0%, var(--sky-blue) 50%, var(--warm-beige) 100%);
  border-bottom: 3px solid var(--sea-blue);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.nav-tabs {
  display: flex;
  justify-content: center;
  list-style: none;
  margin: 0;
  padding: 0;
  flex-wrap: wrap;
}

.nav-tab {
  margin: 0;
}

.nav-button {
  background: none;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
}

.nav-button:hover {
  background-color: var(--shadow-light);
  color: var(--sea-blue);
  transform: translateY(-1px);
}

.nav-button.active {
  background: linear-gradient(135deg, var(--sea-blue) 0%, var(--ocean-teal) 100%);
  color: white;
  border-bottom-color: var(--deep-sea-blue);
  box-shadow: 0 2px 8px var(--shadow-blue);
}

/* Main Content */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: calc(100vh - 200px);
}

/* Section Styles */
.section {
  background: white;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.section-title {
  background: linear-gradient(135deg, var(--sea-blue) 0%, var(--ocean-teal) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
}

.section-content {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
}

/* Rooms Grid */
.rooms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.room-card {
  background: linear-gradient(145deg, var(--warm-beige) 0%, rgba(135, 206, 235, 0.1) 100%);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 15px var(--shadow-blue);
  transition: all 0.3s ease;
  border: 1px solid rgba(74, 144, 184, 0.2);
}

.room-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

.room-image {
  width: 100%;
  height: 250px;
  background-color: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 1.2rem;
}

.room-info {
  padding: 1.5rem;
}

.room-title {
  color: var(--sea-blue);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.amenities {
  list-style: none;
  margin-top: 1rem;
}

.amenities li {
  padding: 0.3rem 0;
  color: #666;
}

.amenities li:before {
  content: "✓ ";
  color: var(--sea-blue);
  font-weight: bold;
}

/* Contact Form */
.contact-form {
  max-width: 600px;
  margin: 2rem auto;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--sea-blue);
  font-weight: 500;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--warm-beige);
  border-radius: 5px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.9);
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--sea-blue);
  box-shadow: 0 0 0 3px var(--shadow-light);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-button {
  background: linear-gradient(135deg, var(--sea-blue) 0%, var(--ocean-teal) 50%, var(--primary-olive) 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 500;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  box-shadow: 0 2px 10px var(--shadow-blue);
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px var(--shadow-medium);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Recommendations */
.filter-container {
  margin: 2rem 0;
  text-align: center;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  border: 2px solid var(--sea-blue);
  background: white;
  color: var(--sea-blue);
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}

.filter-button:hover {
  background: var(--shadow-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-light);
}

.filter-button.active {
  background: linear-gradient(135deg, var(--sea-blue) 0%, var(--ocean-teal) 100%);
  color: white;
  box-shadow: 0 4px 15px var(--shadow-medium);
}

.filter-icon {
  font-size: 1.2rem;
}

.filter-label {
  font-weight: 500;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}

.recommendation-card {
  background: linear-gradient(145deg, var(--warm-beige) 0%, rgba(135, 206, 235, 0.08) 100%);
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px var(--shadow-blue);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(74, 144, 184, 0.15);
}

.recommendation-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

.recommendation-title {
  color: var(--sea-blue);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.recommendation-type {
  background: linear-gradient(135deg, var(--sea-blue) 0%, var(--ocean-teal) 100%);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.9rem;
  display: inline-block;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px var(--shadow-light);
}

/* Success Message */
.success-message {
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 5px;
  margin-bottom: 1rem;
  text-align: center;
}

/* Footer */
.footer {
  background: linear-gradient(135deg, var(--primary-olive) 0%, var(--ocean-teal) 50%, var(--sea-blue) 100%);
  color: white;
  text-align: center;
  padding: 2rem;
  margin-top: 3rem;
  box-shadow: 0 -4px 20px var(--shadow-blue);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-tabs {
    flex-direction: column;
  }
  
  .nav-button {
    padding: 0.8rem 1rem;
    text-align: center;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .section {
    padding: 1.5rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .logo {
    font-size: 2rem;
  }
  
  .rooms-grid,
  .recommendations-grid {
    grid-template-columns: 1fr;
  }

  .filter-buttons {
    flex-direction: column;
    align-items: center;
  }

  .filter-button {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }
}
