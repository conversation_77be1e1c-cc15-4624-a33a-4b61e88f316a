/* Color Theme: <PERSON><PERSON> (#9CAF88), White (#FFFFFF), Beige (#F5F5DC) */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: #FFFFFF;
  color: #333;
  line-height: 1.6;
}

.app {
  min-height: 100vh;
}

/* Header Styles */
.header {
  background: linear-gradient(135deg, #9CAF88 0%, #8FA076 100%);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.logo {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.tagline {
  font-size: 1.1rem;
  font-weight: 300;
  opacity: 0.9;
}

/* Navigation Styles */
.navigation {
  background-color: #F5F5DC;
  border-bottom: 3px solid #9CAF88;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.nav-tabs {
  display: flex;
  justify-content: center;
  list-style: none;
  margin: 0;
  padding: 0;
  flex-wrap: wrap;
}

.nav-tab {
  margin: 0;
}

.nav-button {
  background: none;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
}

.nav-button:hover {
  background-color: rgba(156, 175, 136, 0.1);
  color: #9CAF88;
}

.nav-button.active {
  background-color: #9CAF88;
  color: white;
  border-bottom-color: #8FA076;
}

/* Main Content */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: calc(100vh - 200px);
}

/* Section Styles */
.section {
  background: white;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.section-title {
  color: #9CAF88;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
}

.section-content {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
}

/* Rooms Grid */
.rooms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.room-card {
  background: #F5F5DC;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.room-card:hover {
  transform: translateY(-5px);
}

.room-image {
  width: 100%;
  height: 250px;
  background-color: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 1.2rem;
}

.room-info {
  padding: 1.5rem;
}

.room-title {
  color: #9CAF88;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.amenities {
  list-style: none;
  margin-top: 1rem;
}

.amenities li {
  padding: 0.3rem 0;
  color: #666;
}

.amenities li:before {
  content: "✓ ";
  color: #9CAF88;
  font-weight: bold;
}

/* Contact Form */
.contact-form {
  max-width: 600px;
  margin: 2rem auto;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  color: #9CAF88;
  font-weight: 500;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #F5F5DC;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #9CAF88;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-button {
  background: linear-gradient(135deg, #9CAF88 0%, #8FA076 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 500;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(156, 175, 136, 0.3);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Recommendations */
.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.recommendation-card {
  background: #F5F5DC;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
  cursor: pointer;
}

.recommendation-card:hover {
  transform: translateY(-3px);
}

.recommendation-title {
  color: #9CAF88;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.recommendation-type {
  background: #9CAF88;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.9rem;
  display: inline-block;
  margin-bottom: 1rem;
}

/* Success Message */
.success-message {
  background: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 5px;
  margin-bottom: 1rem;
  text-align: center;
}

/* Footer */
.footer {
  background-color: #9CAF88;
  color: white;
  text-align: center;
  padding: 2rem;
  margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-tabs {
    flex-direction: column;
  }
  
  .nav-button {
    padding: 0.8rem 1rem;
    text-align: center;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .section {
    padding: 1.5rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .logo {
    font-size: 2rem;
  }
  
  .rooms-grid,
  .recommendations-grid {
    grid-template-columns: 1fr;
  }
}
