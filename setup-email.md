# Email Setup Instructions for Relax Project Crete

## Quick Setup Steps

### 1. Gmail App Password Setup
1. Go to https://myaccount.google.com/security
2. Enable 2-Factor Authentication if not already enabled
3. Click "App passwords" 
4. Select "Mail" and "Other (Custom name)"
5. Enter "Relax Project Crete" as the name
6. Copy the 16-character password (format: abcd efgh ijkl mnop)

### 2. Configure Your Email Settings

**Option A: Using Environment Variables (Recommended)**

Edit the file `backend/.env` and replace the placeholder values:

```
GMAIL_USERNAME=<EMAIL>
GMAIL_APP_PASSWORD=your-16-character-app-password
OWNER_EMAIL=<EMAIL>
```

**Option B: Direct Configuration**

Edit `backend/src/main/resources/application.properties` and replace:
- `<EMAIL>` with your actual Gmail address
- `your-app-password` with your 16-character app password
- `<EMAIL>` with the email where you want to receive inquiries

### 3. Test the Email Functionality

1. Make sure both frontend and backend are running:
   ```bash
   # Terminal 1 - Frontend
   cd frontend && npm start
   
   # Terminal 2 - Backend  
   cd backend && mvn spring-boot:run
   ```

2. Go to http://localhost:3000/book
3. Fill out the booking form with test data
4. Submit the form
5. Check your email for:
   - Inquiry notification (sent to business email)
   - Confirmation email (sent to customer email)

### 4. Email Templates

**Business Notification Email:**
- Subject: "New Booking Inquiry - [Customer Name]"
- Contains: Customer details, booking dates, room preference, special requests
- Sent to: Your business email

**Customer Confirmation Email:**
- Subject: "Thank you for your inquiry - Relax Project Crete"
- Contains: Booking details confirmation, contact information, response time promise
- Sent to: Customer's email address

### 5. Troubleshooting

**Common Issues:**

1. **"Authentication failed" error:**
   - Make sure 2-Factor Authentication is enabled
   - Use App Password, not your regular Gmail password
   - Check that the email address is correct

2. **"Connection refused" error:**
   - Check your internet connection
   - Verify Gmail SMTP settings are correct
   - Try restarting the backend server

3. **Emails not being received:**
   - Check spam/junk folders
   - Verify the recipient email addresses are correct
   - Check backend console for error messages

4. **Backend won't start:**
   - Make sure Java 17+ is installed
   - Check that port 8080 is not in use
   - Verify Maven is properly installed

### 6. Security Notes

- Never commit your .env file to version control
- Use App Passwords instead of your main Gmail password
- Consider using a dedicated business Gmail account
- The .env file is already added to .gitignore for security

### 7. Production Deployment

When deploying to production:
- Set environment variables on your hosting platform
- Use a professional email address (e.g., <EMAIL>)
- Consider using a dedicated email service like SendGrid for higher volume

### 8. Testing Checklist

- [ ] Gmail App Password generated
- [ ] Environment variables configured
- [ ] Backend starts without errors
- [ ] Frontend connects to backend
- [ ] Test booking form submission
- [ ] Business notification email received
- [ ] Customer confirmation email received
- [ ] Email content looks professional
- [ ] All links and contact info are correct

## Support

If you encounter any issues:
1. Check the backend console for error messages
2. Verify all email settings are correct
3. Test with a simple email first
4. Check Gmail security settings

The email system is designed to be reliable and professional, providing quick responses to your guests as promised on the website.
