# Crete Rooms - Vacation Rental Website

A modern, responsive website for a vacation rental business in Crete, Greece. Built with React frontend and Java Spring Boot backend.

## Features

- **5 Main Sections:**
  - About Us - Company information and story
  - Rooms - Display of 3 apartments with amenities
  - Contact Us - Contact information and quick response guarantee
  - Recommendations Around - Interactive local recommendations for food and attractions
  - Book Now - Inquiry form with email notifications

- **Design:**
  - Responsive design optimized for mobile and desktop
  - Color theme: Pale olive, white, and beige
  - Modern, clean interface with smooth transitions

- **Backend Features:**
  - Gmail integration for email notifications
  - Form validation and error handling
  - Quick response system (responds within minutes)
  - RESTful API design

## Technology Stack

- **Frontend:** React 18, CSS3, Axios
- **Backend:** Java 17, Spring Boot 3.2, Spring Mail
- **Email:** Gmail SMTP integration

## Setup Instructions

### Prerequisites

- Node.js 16+ and npm
- Java 17+
- Maven 3.6+
- Gmail account with App Password enabled

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```

The frontend will be available at `http://localhost:3000`

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Configure email settings in `src/main/resources/application.properties`:
   ```properties
   spring.mail.username=<EMAIL>
   spring.mail.password=your-app-password
   app.email.from=<EMAIL>
   app.email.to=<EMAIL>
   ```

   Or set environment variables:
   ```bash
   export GMAIL_USERNAME=<EMAIL>
   export GMAIL_APP_PASSWORD=your-app-password
   export OWNER_EMAIL=<EMAIL>
   ```

3. Run the application:
   ```bash
   mvn spring-boot:run
   ```

The backend will be available at `http://localhost:8080`

### Gmail App Password Setup

1. Enable 2-Factor Authentication on your Gmail account
2. Go to Google Account settings > Security > App passwords
3. Generate a new app password for "Mail"
4. Use this app password (not your regular password) in the configuration

## Project Structure

```
crete-rooms/
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   │   ├── AboutUs.js
│   │   │   ├── BookNow.js
│   │   │   ├── ContactUs.js
│   │   │   ├── Navigation.js
│   │   │   ├── Recommendations.js
│   │   │   └── Rooms.js
│   │   ├── App.js
│   │   ├── App.css
│   │   └── index.js
│   └── package.json
├── backend/
│   ├── src/main/java/com/crete/rooms/
│   │   ├── controller/
│   │   ├── model/
│   │   ├── service/
│   │   └── Application.java
│   ├── src/main/resources/
│   └── pom.xml
└── README.md
```

## API Endpoints

- `POST /api/inquiry` - Submit booking inquiry
- `GET /api/health` - Health check endpoint

## Customization

### Adding Content

1. **About Us:** Edit `frontend/src/components/AboutUs.js`
2. **Rooms:** Update room data in `frontend/src/components/Rooms.js`
3. **Recommendations:** Modify recommendations array in `frontend/src/components/Recommendations.js`
4. **Contact Info:** Update contact details in `frontend/src/components/ContactUs.js`

### Adding Images

1. Place images in `frontend/public/images/`
2. Update image references in components
3. Replace placeholder image divs with actual `<img>` tags

### Styling

- Main styles are in `frontend/src/App.css`
- Color theme variables can be updated at the top of the CSS file
- Responsive breakpoints are defined in the media queries section

## Deployment Considerations

### Frontend Deployment
- Build the production version: `npm run build`
- Deploy the `build` folder to your hosting service
- Update API endpoints if backend is hosted separately

### Backend Deployment
- Package the application: `mvn clean package`
- Deploy the generated JAR file
- Set production environment variables for email configuration
- Ensure proper CORS configuration for your domain

### Environment Variables for Production
```bash
GMAIL_USERNAME=<EMAIL>
GMAIL_APP_PASSWORD=your-production-app-password
OWNER_EMAIL=<EMAIL>
```

## Future Enhancements

- Integration with Airbnb and Booking.com APIs
- Payment processing
- Multi-language support (Greek/English)
- Image gallery with lightbox
- Google Maps integration
- SEO optimization
- Progressive Web App features
- Admin panel for content management

## Support

For questions or issues, please contact the development team or refer to the documentation in each component file.
